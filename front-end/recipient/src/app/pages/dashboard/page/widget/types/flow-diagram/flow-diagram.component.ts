import { Component, Input } from '@angular/core';
import { Widget } from '@api/dashboard/models/widget.interface';
import { FFlowComponent, FFlowModule } from '@foblex/flow';
import {
  FlowDiagramEntryResponse,
  FlowDiagramResponse,
} from '@api/dashboard/responses/widget/flow-diagram.response';

@Component({
  selector: 'widget-flow-diagram',
  imports: [FFlowComponent, FFlowModule],
  templateUrl: './flow-diagram.component.html',
  styleUrl: './flow-diagram.component.scss',
})
export class FlowDiagramComponent {
  @Input({ required: true }) widget!: Widget;
  @Input({ required: true }) statistics!: FlowDiagramResponse;

  public readonly eMarkerType = eMarker;

  public getGroupFarthestXNumber(index: number): number {
    if (index === 0) {
      return 0;
    }

    const group = this.statistics.groups.at(index);

    const entry = this.statistics.entries
      .filter((entry) => entry.group_id === group?.id)
      .sort((a, b) => b.position.x - a.position.x)
      .at(0);

    return entry?.position.x ?? 0;
  }
}
