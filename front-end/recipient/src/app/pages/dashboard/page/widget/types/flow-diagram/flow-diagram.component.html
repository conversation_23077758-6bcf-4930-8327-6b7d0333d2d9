<div class="bg-white w-full h-full p-4 rounded-lg shadow-sm border border-gray-100">
  <!-- Global SVG definitions for connection markers -->
  <svg width="0" height="0" style="position: absolute;">
    <defs>
      <marker id="connection-input" markerWidth="8" markerHeight="8" refX="7" refY="4" orient="auto" markerUnits="strokeWidth">
        <circle cx="4" cy="4" r="3" fill="#235FE7" stroke="white" stroke-width="1"/>
      </marker>
      <marker id="connection-output" markerWidth="10" markerHeight="8" refX="1" refY="4" orient="auto" markerUnits="strokeWidth">
        <polygon points="2,1 2,7 8,4" fill="#235FE7" stroke="white" stroke-width="0.5"/>
      </marker>
    </defs>
  </svg>

  <f-flow fDraggable>
    <f-canvas>

      @for(group of statistics.groups; track group.id; let index = $index) {
        <div fGroup [fGroupId]="group.id"
             [fGroupPosition]="{ x: group.x, y: 0 }" [fGroupSize]="{ width: group.width, height: 50 }">
          <div class="group-content">
            <p class="group-title">{{ group.name }}</p>
          </div>
        </div>
      }

      @for(connection of statistics.connections; track $index) {
        <f-connection [fOutputId]="connection.output_id" [fInputId]="connection.input_id" fBehavior="floating" fType="straight"></f-connection>
      }

      @for(entry of statistics.entries; track $index) {
        <div
          fNode
          fNodeOutput
          fNodeInput
          [fNodePosition]="{ x: entry.position.x, y: entry.position.y }"
          [fOutputId]="entry.output_id"
          [fInputId]="entry.input_id"
          [fNodeParentId]="entry.group_id"
          [fNodeSelectionDisabled]="true"
        >
          <!-- Output marker for nodes with both input and output -->
          <div class="node-output-marker"></div>

          <div class="node-content">
            <p class="node-title">{{ entry.name }}</p>

            @if(entry.value_formatted) {
              <p class="node-value">
                @if(entry.prefix) {
                  <span>{{ entry.prefix }}</span>
                }
                {{ entry.value_formatted }}
                @if(entry.suffix) {
                  <span>{{ entry.suffix }}</span>
                }
              </p>
            }

            @if(entry.comparison_formatted && entry.comparison_value !== null) {
              <p class="node-comparison"
                 [class.positive]="entry.comparison_value > 0"
                 [class.negative]="entry.comparison_value < 0"
                 [class.neutral]="entry.comparison_value === 0">
                {{ entry.comparison_formatted }}
              </p>
            }
          </div>
        </div>
      }
    </f-canvas>
  </f-flow>

</div>
