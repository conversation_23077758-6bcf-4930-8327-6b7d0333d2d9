<div class="bg-white w-full h-full p-4 rounded-lg shadow-sm border border-gray-100">
  <f-flow fDraggable>
    <f-canvas>

      @for(group of statistics.groups; track group.id; let index = $index) {
        <div fGroup [fGroupId]="group.id"
             [fGroupPosition]="{ x: group.x, y: 0 }" [fGroupSize]="{ width: group.width, height: 50 }">
          <div class="group-content">
            <p class="group-title">{{ group.name }}</p>
          </div>
        </div>
      }

      @for(connection of statistics.connections; track $index) {
        <f-connection [fOutputId]="connection.output_id" [fInputId]="connection.input_id" fBehavior="floating" fType="straight"></f-connection>
      }

      @for(entry of statistics.entries; track $index) {
        <div
          fNode
          fNodeOutput
          fNodeInput
          [fNodePosition]="{ x: entry.position.x, y: entry.position.y }"
          [fOutputId]="entry.output_id"
          [fInputId]="entry.input_id"
          [fNodeParentId]="entry.group_id"
          [fNodeSelectionDisabled]="true"
        >
          <div class="node-content">
            <p class="node-title">{{ entry.name }}</p>

            @if(entry.value_formatted) {
              <p class="node-value">
                @if(entry.prefix) {
                  <span>{{ entry.prefix }}</span>
                }
                {{ entry.value_formatted }}
                @if(entry.suffix) {
                  <span>{{ entry.suffix }}</span>
                }
              </p>
            }

            @if(entry.comparison_formatted && entry.comparison_value !== null) {
              <p class="node-comparison"
                 [class.positive]="entry.comparison_value > 0"
                 [class.negative]="entry.comparison_value < 0"
                 [class.neutral]="entry.comparison_value === 0">
                {{ entry.comparison_formatted }}
              </p>
            }
          </div>
        </div>
      }
    </f-canvas>
  </f-flow>

</div>
