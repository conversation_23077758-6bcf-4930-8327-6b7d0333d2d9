import { WidgetAccuracy } from '@api/dashboard/support/enums/widget-accuracy.enum';

export interface ChartResponse {
  accuracy: WidgetAccuracy;
  series: ChartSeriesResponse[];
  anomalies: ChartAnomalyResponse[];
}

export interface ChartSeriesResponse {
  name: string;
  data: ChartValueResponse[];
}

export interface ChartValueResponse {
  y: number;
  x: string;
  prefix: string | null;
  suffix: string | null;
  value_formatted: string;
}

export interface ChartAnomalyResponse {
  value: number;
  date: string;
  color: string;
  series_index: number;
}
