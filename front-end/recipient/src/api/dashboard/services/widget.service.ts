import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ConfigService } from '@app/services/config.service';
import { Widget } from '../models/widget.interface';
import { WidgetStatisticsRequest } from '../requests/widget-statistics.request';
import { Observable } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { DataComparisonResponse } from '../responses/widget/data-comparison.response';
import { paramsToHttpParams } from '@app/helpers/transform-params';
import { SummaryResponse } from '../responses/widget/summary.response';
import { ChartResponse } from '../responses/widget/chart.response';

@Injectable({
  providedIn: 'root',
})
export class WidgetService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public statistics(
    widget: Widget,
    request: WidgetStatisticsRequest,
  ): Observable<
    DataResponse<DataComparisonResponse | SummaryResponse | ChartResponse>
  > {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<
      DataResponse<DataComparisonResponse | SummaryResponse | ChartResponse>
    >(`${this.endpoint}/api/v1/recipient/widgets/${widget.id}/statistics`, {
      params,
    });
  }
}
